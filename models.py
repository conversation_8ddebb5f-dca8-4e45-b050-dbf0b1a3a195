from database import db  # Importe l'instance 'db' depuis database.py
from datetime import datetime

class Product(db.Model):
    """
    Modèle représentant un produit dans la base de données.
    created_at sera stocké en UTC.
    """
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False)
    # created_at est stocké en UTC. timezone=False ou omis car nous gérons la conversion.
    # Recommandé: stocker des objets datetime "aware" en UTC.
    # SQLAlchemy peut les stocker correctement si le type est DateTime(timezone=True)
    # et que l'objet datetime que vous lui passez est aware.
    created_at = db.Column(db.DateTime(timezone=True), nullable=False)

    def __repr__(self):
        return f'<Product {self.name}>'