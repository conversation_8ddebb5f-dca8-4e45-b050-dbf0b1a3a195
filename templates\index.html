<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire de Produits</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <h1>Gestionnaire de Produits</h1>
        <form action="/" method="post">
            <div class="form-group">
                <label for="name">Nom du produit :</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label for="price">Prix :</label>
                <input type="number" id="price" name="price" step="0.01" min="0" required>
            </div>
            <div class="form-group">
                <label for="datetime">Date et heure :</label>
                <input type="datetime-local" id="datetime" name="datetime" required>
            </div>
            <div class="form-group">
                <label for="timezone">Afficher/Stocker selon fuseau horaire :</label>
                <select id="timezone" name="timezone">
                    {% for tz in timezones %}
                        <option value="{{ tz }}">{{ tz }}</option>
                    {% endfor %}
                </select>
            </div>
            <button type="submit">Ajouter le Produit</button>
        </form>

        <h2>Liste des Produits</h2>
        <table>
            <thead>
                <tr>
                    <th>Nom</th>
                    <th>Prix (€)</th>
                    <th>Date d'ajout (selon le fuseau horaire sélectionné)</th>
                </tr>
            </thead>
            <tbody id="product-list-body">
                {% for product in products %}
                <tr data-utc-time="{{ product.created_at.isoformat() }}">
                    <td>{{ product.name }}</td>
                    <td>{{ "%.2f"|format(product.price) }}</td>
                    <td class="display-time">{{ product.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td> 
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>