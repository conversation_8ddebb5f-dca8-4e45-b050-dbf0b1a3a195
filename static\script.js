document.addEventListener('DOMContentLoaded', function() {
    const datetimeInput = document.getElementById('datetime');
    const timezoneSelect = document.getElementById('timezone');
    const productListBody = document.getElementById('product-list-body');

    // Détecter le fuseau horaire de l'utilisateur
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    if (userTimezone) {
        timezoneSelect.value = userTimezone;
    }

    // Fonction pour obtenir la date et l'heure locale formatée pour datetime-local
    function getLocalDateTimeString() {
        const now = new Date();
        const year = now.getFullYear();
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // Remplir le champ datetime-local par défaut
    datetimeInput.value = getLocalDateTimeString();

    // Fonction pour convertir et afficher les heures
    function updateDisplayedTimes(selectedTimezone) {
        const productRows = productListBody.querySelectorAll('tr[data-utc-time]');
        productRows.forEach(row => {
            const utcTimeString = row.dataset.utcTime; // Exemple: '2023-10-27T14:30:00+00:00'
            const displayCell = row.querySelector('.display-time');

            if (utcTimeString && displayCell) {
                // Créer un objet Date à partir de la chaîne UTC
                const dateUtc = new Date(utcTimeString);

                // Formatter selon le fuseau horaire sélectionné
                const options = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false, // Format 24 heures
                    timeZone: selectedTimezone
                };
                const formatter = new Intl.DateTimeFormat('fr-FR', options);
                displayCell.textContent = formatter.format(dateUtc);
            }
        });
    }

    // Mise à jour initiale des heures affichées dès le chargement
    updateDisplayedTimes(timezoneSelect.value);

    // Écouteur d'événement pour le changement de fuseau horaire
    timezoneSelect.addEventListener('change', function() {
        updateDisplayedTimes(this.value);
    });
});