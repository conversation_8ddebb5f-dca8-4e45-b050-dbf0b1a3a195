import os
from flask import Flask
from database import db  # Importe l'instance 'db' depuis database.py
import pytz # Pour la liste des fuseaux horaires

# Obtenir le chemin absolu du répertoire du script
basedir = os.path.abspath(os.path.dirname(__file__))

app = Flask(__name__)

# Configuration de la base de données SQLite
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'products.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialiser l'extension avec l'application
db.init_app(app)

# Importation des modèles et des routes APRES avoir initialisé 'db' et 'app'
# pour éviter les problèmes d'importation circulaire.
from models import Product # Nous aurons besoin de Product ici si nous faisons db.create_all()
from routes import init_routes

# Initialiser les routes avec l'application et la base de données
init_routes(app, db, Product, pytz)

# ==============================================================================
# Point d'entrée de l'application
# ==============================================================================
if __name__ == '__main__':
    # Créer les tables de la base de données si elles n'existent pas déjà.
    with app.app_context():
        db.create_all()
    # Lancer le serveur de développement Flask.
    # On met use_reloader=False pour éviter que db.create_all() ne s'exécute deux fois
    # si vous rencontrez des problèmes, ou retirez-le si vous préférez le rechargement automatique.
    app.run(debug=True, port=5001)